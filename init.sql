-- 初始化脚本：创建数据库和用户

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS yingdao 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'yingdao'@'%' IDENTIFIED BY 'oR2QcsfFre.xVV!L';

-- 授予用户对 yingdao 数据库的所有权限
GRANT ALL PRIVILEGES ON yingdao.* TO 'yingdao'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 使用 yingdao 数据库
USE yingdao;

-- 这里可以添加其他初始化表结构的 SQL 语句
-- 例如：
-- CREATE TABLE IF NOT EXISTS users (
--     id INT AUTO_INCREMENT PRIMARY KEY,
--     username VARCHAR(50) NOT NULL UNIQUE,
--     email VARCHAR(100) NOT NULL UNIQUE,
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );
