[mysqld]
# 基本设置
server-id = 2
bind-address = 0.0.0.0
port = 3306
datadir = /var/lib/mysql
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# SQL模式
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# 连接设置
max_connections = 1000
max_connect_errors = 100000
wait_timeout = 28800
interactive_timeout = 28800

# 内存设置
innodb_buffer_pool_size = 256M
innodb_log_buffer_size = 16M
key_buffer_size = 32M
tmp_table_size = 64M
max_heap_table_size = 64M

# 二进制日志设置
log-bin = mysql-bin
binlog_expire_logs_seconds = 604800
max_binlog_size = 100M

# 中继日志设置
relay-log = relay-bin
relay-log-index = relay-bin.index

# 从服务器设置
read_only = ON
super_read_only = ON

# 复制过滤设置 - 只复制 lnlike 数据库
replicate-do-db = lnlike

# 日志设置
log-error = /var/log/mysql/error.log
slow_query_log = ON
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 性能优化
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = ON

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306
socket = /var/run/mysqld/mysqld.sock
