[mysqld]
# 基本设置
server-id = 2                           # 从服务器ID，必须与主服务器不同
bind-address = 0.0.0.0
port = 3306
datadir = /var/lib/mysql
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# 时区设置
default-time-zone = '+08:00'

# SQL模式
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# 连接设置
max_connections = 1000
max_connect_errors = 100000
wait_timeout = 28800
interactive_timeout = 28800

# 内存设置
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_log_buffer_size = 16M
key_buffer_size = 32M
query_cache_size = 32M
tmp_table_size = 64M
max_heap_table_size = 64M

# 主从复制设置 - 从服务器配置
# 启用二进制日志（从服务器也建议开启，用于级联复制）
log-bin = mysql-bin
binlog_expire_logs_seconds = 604800
max_binlog_size = 100M

# 中继日志设置
relay-log = relay-bin
relay-log-index = relay-bin.index
relay_log_purge = ON

# 从服务器设置
read_only = ON                          # 设置为只读模式
super_read_only = ON                    # 超级用户也只读
replica_skip_errors = 1062,1032         # 跳过常见的复制错误

# 复制过滤设置 - 只复制 lnlike 数据库
replicate-do-db = lnlike               # 只复制 lnlike 数据库
# replicate-ignore-db = mysql          # 忽略系统数据库（可选）
# replicate-ignore-db = information_schema
# replicate-ignore-db = performance_schema
# replicate-ignore-db = sys

# 复制相关设置
replica_net_timeout = 60
replica_parallel_workers = 4            # 并行复制线程数
replica_preserve_commit_order = ON      # 保持提交顺序

# 安全设置
local_infile = OFF
secure_file_priv = /var/lib/mysql-files/

# 日志设置
log-error = /var/log/mysql/error.log
slow_query_log = ON
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 性能优化
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = ON
innodb_open_files = 400

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306
socket = /var/run/mysqld/mysqld.sock
