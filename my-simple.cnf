[mysqld]
# 基本设置
server-id = 2
bind-address = 0.0.0.0
port = 3306

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 连接设置
max_connections = 1000

# 内存设置
innodb_buffer_pool_size = 256M

# 二进制日志设置（主从复制需要）
log-bin = mysql-bin
binlog_expire_logs_seconds = 604800
max_binlog_size = 100M

# 中继日志设置（从服务器）
relay-log = relay-bin
relay-log-index = relay-bin.index

# 从服务器设置
read_only = ON
super_read_only = ON

# 复制过滤设置 - 只复制 lnlike 数据库
replicate-do-db = lnlike

# 复制相关设置（使用新的参数名）
replica_net_timeout = 60
replica_parallel_workers = 4
replica_preserve_commit_order = ON

# 跳过常见的复制错误
replica_skip_errors = 1062,1032

# 日志设置
log-error = /var/log/mysql/error.log
slow_query_log = ON
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 性能优化
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = ON

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306
socket = /var/run/mysqld/mysqld.sock
