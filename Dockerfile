# 使用官方 MySQL 长期支持的最新版镜像作为基础镜像
FROM mysql:lts

# 设置环境变量
ENV MYSQL_ROOT_PASSWORD=oR2QcsfFre.xVV!L
ENV MYSQL_DATABASE=yingdao
ENV MYSQL_USER=yingdao
ENV MYSQL_PASSWORD=oR2QcsfFre.xVV!L

# 设置字符集和排序规则
ENV MYSQL_CHARSET=utf8mb4
ENV MYSQL_COLLATION=utf8mb4_unicode_ci

# 复制自定义配置文件（如果需要的话）
# COPY my.cnf /etc/mysql/conf.d/

# 复制初始化脚本
COPY init.sql /docker-entrypoint-initdb.d/

# 暴露 MySQL 端口
EXPOSE 3306

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 使用默认的 MySQL 启动命令
CMD ["mysqld"]
