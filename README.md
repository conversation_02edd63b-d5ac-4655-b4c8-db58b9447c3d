# YingDao MySQL 部署文档

## 项目概述
这是一个基于 MySQL LTS 官方长期支持版（ 当前为8.4.5）的数据库容器化部署方案，专为阿里云环境设计。

## 配置信息
- **数据库版本**: MySQL LTS
- **数据库名称**: yingdao
- **用户名**: yingdao
- **密码**: oR2QcsfFre.xVV!L
- **外网端口**: 23316
- **内部端口**: 3306

## 文件说明
- `Dockerfile`: MySQL 容器构建文件
- `docker-compose.yml`: 容器编排配置文件
- `my.cnf`: MySQL 自定义配置文件（包含主从复制从服务器配置）
- `init.sql`: 数据库初始化脚本
- `setup-master.sql`: 主服务器配置脚本
- `setup-slave.sql`: 从服务器配置脚本
- `README.md`: 部署说明文档

## 部署步骤

### 1. 准备环境
确保服务器已安装 Docker 和 Docker Compose：
```bash
# 安装 Docker
curl -fsSL https://get.docker.com | bash

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 上传文件
将所有文件上传到阿里云服务器的目标目录。

### 3. 构建和启动
```bash
# 构建并启动容器
docker-compose up -d

# 查看容器状态
docker-compose ps

# 查看日志
docker-compose logs -f mysql
```

### 4. 验证部署
```bash
# 连接测试
mysql -h localhost -P 23316 -u yingdao -p
# 输入密码: oR2QcsfFre.xVV!L
```

## 阿里云安全组配置
确保在阿里云控制台的安全组中开放端口 23316：
- 协议类型: TCP
- 端口范围: 23316/23316
- 授权对象: 0.0.0.0/0 (或根据需要限制IP范围)

## 数据持久化
- 数据文件存储在 Docker volume `mysql_data` 中
- 日志文件映射到 `./logs` 目录

## 常用命令
```bash
# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看数据库状态
docker-compose exec mysql mysql -u root -p -e "SHOW DATABASES;"

# 备份数据库
docker-compose exec mysql mysqldump -u root -p yingdao > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u root -p yingdao < backup.sql
```

## 主从复制配置

### 从服务器特性
- **服务器ID**: 2（在 my.cnf 中配置）
- **只读模式**: 启用（read_only = ON）
- **复制过滤**: 只同步 lnlike 数据库
- **并行复制**: 启用4个工作线程

### 配置步骤

#### 1. 主服务器配置
在主服务器上执行：
```sql
-- 执行主服务器配置脚本
source setup-master.sql;
```

#### 2. 从服务器配置
1. 启动从服务器容器
2. 修改 `setup-slave.sql` 中的连接参数：
   - MASTER_HOST: 主服务器IP
   - MASTER_LOG_FILE: 从主服务器获取的日志文件名
   - MASTER_LOG_POS: 从主服务器获取的日志位置
3. 执行从服务器配置：
```bash
docker-compose exec mysql mysql -u root -p < setup-slave.sql
```

#### 3. 验证复制状态
```sql
SHOW SLAVE STATUS\G
```
确认以下状态：
- Slave_IO_Running: Yes
- Slave_SQL_Running: Yes
- Seconds_Behind_Master: 0 或较小数字

### 复制监控
```bash
# 检查复制状态
docker-compose exec mysql mysql -u root -p -e "SHOW SLAVE STATUS\G"

# 查看错误日志
docker-compose logs mysql
```

## 注意事项
1. 请妥善保管数据库密码
2. 定期备份数据库
3. 监控服务器资源使用情况
4. 根据实际需求调整 MySQL 配置参数
5. 定期检查主从复制状态
6. 确保主从服务器时间同步
7. 监控复制延迟情况
