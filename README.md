# YingDao MySQL 部署文档

## 项目概述
这是一个基于 MySQL LTS 官方长期支持版（ 当前为8.4.5）的数据库容器化部署方案，专为阿里云环境设计。

## 配置信息
- **数据库版本**: MySQL LTS
- **数据库名称**: yingdao
- **用户名**: yingdao
- **密码**: oR2QcsfFre.xVV!L
- **外网端口**: 23316
- **内部端口**: 3306

## 文件说明
- `Dockerfile`: MySQL 容器构建文件
- `docker-compose.yml`: 容器编排配置文件
- `init.sql`: 数据库初始化脚本
- `README.md`: 部署说明文档

## 部署步骤

### 1. 准备环境
确保服务器已安装 Docker 和 Docker Compose：
```bash
# 安装 Docker
curl -fsSL https://get.docker.com | bash

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 上传文件
将所有文件上传到阿里云服务器的目标目录。

### 3. 构建和启动
```bash
# 构建并启动容器
docker-compose up -d

# 查看容器状态
docker-compose ps

# 查看日志
docker-compose logs -f mysql
```

### 4. 验证部署
```bash
# 连接测试
mysql -h localhost -P 23316 -u yingdao -p
# 输入密码: oR2QcsfFre.xVV!L
```

## 阿里云安全组配置
确保在阿里云控制台的安全组中开放端口 23316：
- 协议类型: TCP
- 端口范围: 23316/23316
- 授权对象: 0.0.0.0/0 (或根据需要限制IP范围)

## 数据持久化
- 数据文件存储在 Docker volume `mysql_data` 中
- 日志文件映射到 `./logs` 目录

## 常用命令
```bash
# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看数据库状态
docker-compose exec mysql mysql -u root -p -e "SHOW DATABASES;"

# 备份数据库
docker-compose exec mysql mysqldump -u root -p yingdao > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u root -p yingdao < backup.sql
```

## 注意事项
1. 请妥善保管数据库密码
2. 定期备份数据库
3. 监控服务器资源使用情况
4. 根据实际需求调整 MySQL 配置参数
