-- 主从复制从服务器配置脚本
-- 在从服务器上执行此脚本来设置主从复制

-- 停止从服务器（如果已经在运行）
STOP SLAVE;

-- 重置从服务器状态
RESET SLAVE ALL;

-- 配置主服务器连接信息
-- 请根据实际情况修改以下参数：
-- MASTER_HOST: 主服务器IP地址
-- MASTER_PORT: 主服务器端口
-- MASTER_USER: 复制用户名
-- MASTER_PASSWORD: 复制用户密码
-- MASTER_LOG_FILE: 主服务器当前二进制日志文件名
-- MASTER_LOG_POS: 主服务器当前二进制日志位置

CHANGE MASTER TO
    MASTER_HOST='*************',           -- 替换为实际的主服务器IP
    MASTER_PORT=53306,                   -- 主服务器端口
    MASTER_USER='repl_user',     -- 复制用户名
    MASTER_PASSWORD='wYoQ7YrX8dcZWFFf', -- 复制用户密码
    MASTER_LOG_FILE='mysql-bin.000007', -- 从主服务器获取的日志文件名
    MASTER_LOG_POS=18188459,                 -- 从主服务器获取的日志位置
    MASTER_CONNECT_RETRY=10,            -- 连接重试间隔（秒）
    MASTER_RETRY_COUNT=86400;           -- 重试次数

-- 启动从服务器
START SLAVE;

-- 检查从服务器状态
SHOW SLAVE STATUS\G

-- 验证复制状态的关键指标：
-- Slave_IO_Running: Yes
-- Slave_SQL_Running: Yes
-- Seconds_Behind_Master: 应该是一个较小的数字或0
-- Last_IO_Error: 应该为空
-- Last_SQL_Error: 应该为空
