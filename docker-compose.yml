services:
  mysql:
    build: .
    container_name: mysql8
    restart: unless-stopped
    user: "999:999"
    environment:
      MYSQL_ROOT_PASSWORD: oR2QcsfFre.xVV!L
      MYSQL_DATABASE: yingdao
      MYSQL_USER: yingdao
      MYSQL_PASSWORD: oR2QcsfFre.xVV!L
      TZ: Asia/Shanghai
    ports:
      - "23316:3306"  # 外网端口23316映射到容器内部3306端口
    volumes:
      - /data/yingdao/mysql:/var/lib/mysql
      - ./my.cnf:/etc/mysql/my.cnf:ro
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./logs:/var/log/mysql
    networks:
      - yingdao-network
    # 使用自定义 my.cnf 配置文件，不需要命令行参数

networks:
  yingdao-network:
    driver: bridge