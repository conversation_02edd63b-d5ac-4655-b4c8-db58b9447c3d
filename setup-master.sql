-- 主服务器配置脚本
-- 在主服务器上执行此脚本来准备主从复制

-- 创建复制用户
CREATE USER IF NOT EXISTS 'replication_user'@'%' IDENTIFIED BY 'replication_password';

-- 授予复制权限
GRANT REPLICATION SLAVE ON *.* TO 'replication_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 锁定表以获取一致性快照（可选，用于初始数据同步）
-- FLUSH TABLES WITH READ LOCK;

-- 获取当前二进制日志文件和位置
SHOW MASTER STATUS;

-- 注意：记录上面命令输出的 File 和 Position 值
-- 这些值需要在从服务器的 CHANGE MASTER TO 命令中使用

-- 如果之前锁定了表，现在解锁
-- UNLOCK TABLES;

-- 验证二进制日志是否启用
SHOW VARIABLES LIKE 'log_bin';

-- 查看二进制日志文件列表
SHOW BINARY LOGS;

-- 查看当前服务器ID
SHOW VARIABLES LIKE 'server_id';
