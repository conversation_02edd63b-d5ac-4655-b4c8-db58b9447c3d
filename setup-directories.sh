#!/bin/bash

# 创建必要的目录并设置权限
echo "正在创建 MySQL 数据和日志目录..."

# 创建数据目录
sudo mkdir -p /data/yingdao/mysql
sudo mkdir -p /data/yingdao/logs

# 设置目录权限 (MySQL 容器内的 mysql 用户 UID 通常是 999)
sudo chown -R 999:999 /data/yingdao/mysql
sudo chown -R 999:999 /data/yingdao/logs

# 设置目录权限
sudo chmod -R 755 /data/yingdao/mysql
sudo chmod -R 755 /data/yingdao/logs

echo "目录创建和权限设置完成！"
echo "数据目录: /data/yingdao/mysql"
echo "日志目录: /data/yingdao/logs"

# 显示目录信息
ls -la /data/yingdao/
